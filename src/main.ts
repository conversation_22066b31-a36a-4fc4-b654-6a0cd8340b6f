import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { App } from './app/app';
import Keycloak from 'keycloak-js';
import {provideKeycloak} from './app/providers/keycloak.provider';


const keycloak = new Keycloak({
  url: 'https://your-keycloak-server/auth',
  realm: 'your-realm',
  clientId: 'your-client-id',
});

keycloak
  .init({
    onLoad: 'check-sso',
    pkceMethod: 'S256',
    silentCheckSsoRedirectUri: window.location.origin + 'public/silent-check-sso.html',
  })
  .then((authenticated) => {
    console.log('Keycloak init:', authenticated);
    return bootstrapApplication(App, {
      providers: [...appConfig.providers, provideKeycloak(keycloak)],
    });
  })
  .catch((err) => console.error('Keycloak init failed', err));
