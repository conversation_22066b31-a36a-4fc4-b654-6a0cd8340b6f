<!-- Loading State - Show while authentication is being checked -->
<div *ngIf="!isAuthInitialized" class="flex items-center justify-center min-h-screen bg-white">
  <div class="text-center">
    <!-- Loading Spinner -->
    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
    <h2 class="text-xl font-semibold text-gray-700 mb-2">Loading...</h2>
    <p class="text-gray-500">Checking authentication status</p>
  </div>
</div>

<!-- Main App Layout - Only show when authentication is initialized and user is authenticated -->
<div *ngIf="isAuthInitialized && isAuthenticated" class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden">
  <!-- Mobile Sidebar -->
  <app-mobile-sidebar
    [isOpen]="isMobileSidebarOpen"
    (close)="closeMobileSidebar()">
  </app-mobile-sidebar>

  <div class="layout-container flex h-full grow flex-col">
    <!-- Mobile Header (visible on small screens) -->
    <div class="md:hidden">
      <app-header (toggleSidebar)="toggleMobileSidebar()"></app-header>
    </div>

    <div class="flex flex-1">
      <!-- Sidebar (hidden on mobile, visible on desktop) -->
      <div class="hidden md:block">
        <app-sidebar></app-sidebar>
      </div>

      <!-- Main Content Area -->
      <div class="flex flex-col flex-1 min-w-0">
        <!-- Desktop Header (hidden on mobile) -->
        <div class="hidden md:block">
          <app-header (toggleSidebar)="toggleMobileSidebar()"></app-header>
        </div>

        <!-- Page Content -->
        <div class="flex-1 overflow-auto">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <div class="md:hidden border-t border-[#dbe1e6] bg-white">
      <app-mobile-nav></app-mobile-nav>
    </div>
  </div>
</div>

<!-- Unauthenticated State - Show a message if auth is initialized but user is not authenticated -->
<div *ngIf="isAuthInitialized && !isAuthenticated" class="flex items-center justify-center min-h-screen bg-white">
  <div class="text-center">
    <h2 class="text-xl font-semibold text-gray-700 mb-2">Redirecting to login...</h2>
    <p class="text-gray-500">You will be redirected to the login page shortly</p>
  </div>
</div>
