<div class="layout-content-container flex flex-col w-full flex-1">
  <!-- Page Header -->
  <div class="flex flex-col sm:flex-row sm:justify-between gap-3 p-4">
    <div class="flex flex-col gap-1">
      <p class="text-[#111518] tracking-light text-2xl md:text-[32px] font-bold leading-tight">Settings</p>
      <p class="text-[#617789] text-sm md:text-base font-normal leading-normal">Manage your account preferences and privacy settings</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-2">
      <button
        (click)="resetSettings()"
        class="flex w-full sm:w-auto min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-bold leading-normal tracking-[0.015em]"
      >
        Reset
      </button>
      <button
        (click)="saveSettings()"
        class="flex w-full sm:w-auto min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#2094f3] text-white text-sm font-bold leading-normal tracking-[0.015em]"
      >
        Save Changes
      </button>
    </div>
  </div>

  <!-- Settings Sections -->
  <div class="p-4">
    <div class="flex flex-col gap-8">
      <div
        *ngFor="let section of settingsSections; let sectionIndex = index"
        class="flex flex-col gap-4"
      >
        <!-- Section Header -->
        <div class="flex flex-col gap-1">
          <h2 class="text-[#111518] text-lg md:text-[22px] font-bold leading-tight tracking-[-0.015em]">{{ section.title }}</h2>
          <p class="text-[#617789] text-sm md:text-base font-normal leading-normal">{{ section.description }}</p>
        </div>

        <!-- Settings List -->
        <div class="flex flex-col gap-4 rounded-xl border border-[#dbe1e6] p-4 md:p-6">
          <div
            *ngFor="let setting of section.settings; let settingIndex = index"
            class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 py-3"
            [class.border-b]="settingIndex < section.settings.length - 1"
            [class.border-[#dbe1e6]]="settingIndex < section.settings.length - 1"
          >
            <div class="flex flex-col gap-1 flex-1">
              <label class="text-[#111518] text-sm md:text-base font-medium leading-normal">{{ setting.label }}</label>
              <p *ngIf="setting.description" class="text-[#617789] text-xs md:text-sm font-normal leading-normal">{{ setting.description }}</p>
            </div>

            <!-- Toggle Setting -->
            <div *ngIf="setting.type === 'toggle'" class="flex items-center">
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  [checked]="setting.value"
                  (change)="updateSetting(sectionIndex, settingIndex, $any($event.target).checked)"
                  class="sr-only peer"
                >
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <!-- Select Setting -->
            <div *ngIf="setting.type === 'select'" class="flex items-center">
              <select
                [value]="setting.value"
                (change)="updateSetting(sectionIndex, settingIndex, $any($event.target).value)"
                class="form-select rounded-lg border border-[#dbe1e6] bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option *ngFor="let option of setting.options" [value]="option">
                  {{ option | titlecase }}
                </option>
              </select>
            </div>

            <!-- Number Input Setting -->
            <div *ngIf="setting.type === 'number'" class="flex items-center">
              <input
                type="number"
                [value]="setting.value"
                (input)="updateSetting(sectionIndex, settingIndex, $any($event.target).value)"
                class="form-input rounded-lg border border-[#dbe1e6] bg-white px-3 py-2 text-sm w-20 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>

            <!-- Text Input Setting -->
            <div *ngIf="setting.type === 'input'" class="flex items-center">
              <input
                type="text"
                [value]="setting.value"
                (input)="updateSetting(sectionIndex, settingIndex, $any($event.target).value)"
                class="form-input rounded-lg border border-[#dbe1e6] bg-white px-3 py-2 text-sm w-48 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Danger Zone -->
  <div class="p-4">
    <div class="flex flex-col gap-4 rounded-xl border border-red-200 bg-red-50 p-6">
      <div class="flex flex-col gap-1">
        <h2 class="text-red-800 text-[22px] font-bold leading-tight tracking-[-0.015em]">Danger Zone</h2>
        <p class="text-red-600 text-base font-normal leading-normal">These actions cannot be undone</p>
      </div>

      <div class="flex flex-col gap-3">
        <div class="flex items-center justify-between gap-4 py-3 border-b border-red-200">
          <div class="flex flex-col gap-1">
            <label class="text-red-800 text-base font-medium leading-normal">Delete Account</label>
            <p class="text-red-600 text-sm font-normal leading-normal">Permanently delete your account and all associated data</p>
          </div>
          <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-red-600 text-white text-sm font-bold leading-normal tracking-[0.015em]">
            Delete Account
          </button>
        </div>

        <div class="flex items-center justify-between gap-4 py-3">
          <div class="flex flex-col gap-1">
            <label class="text-red-800 text-base font-medium leading-normal">Reset All Data</label>
            <p class="text-red-600 text-sm font-normal leading-normal">Reset all your data to default values</p>
          </div>
          <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-red-600 text-white text-sm font-bold leading-normal tracking-[0.015em]">
            Reset Data
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
