import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SettingsSection } from '../../models/dashboard.models';

@Component({
  selector: 'app-settings',
  imports: [CommonModule, FormsModule],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.css'
})
export class SettingsComponent {
  settingsSections: SettingsSection[] = [
    {
      title: 'Notifications',
      description: 'Manage your notification preferences',
      settings: [
        {
          key: 'emailNotifications',
          label: 'Email Notifications',
          type: 'toggle',
          value: true,
          description: 'Receive notifications via email'
        },
        {
          key: 'smsNotifications',
          label: 'SMS Notifications',
          type: 'toggle',
          value: false,
          description: 'Receive notifications via SMS'
        },
        {
          key: 'maintenanceAlerts',
          label: 'Maintenance Alerts',
          type: 'toggle',
          value: true,
          description: 'Get notified about maintenance activities'
        },
        {
          key: 'announcementAlerts',
          label: 'Announcement Alerts',
          type: 'toggle',
          value: true,
          description: 'Get notified about new announcements'
        }
      ]
    },
    {
      title: 'Privacy',
      description: 'Control your privacy settings',
      settings: [
        {
          key: 'profileVisibility',
          label: 'Profile Visibility',
          type: 'select',
          value: 'residents',
          options: ['public', 'residents', 'private'],
          description: 'Who can see your profile information'
        },
        {
          key: 'shareContactInfo',
          label: 'Share Contact Information',
          type: 'toggle',
          value: false,
          description: 'Allow other residents to see your contact info'
        }
      ]
    },
    {
      title: 'Account',
      description: 'Manage your account settings',
      settings: [
        {
          key: 'language',
          label: 'Language',
          type: 'select',
          value: 'english',
          options: ['english', 'spanish', 'french'],
          description: 'Choose your preferred language'
        },
        {
          key: 'timezone',
          label: 'Timezone',
          type: 'select',
          value: 'est',
          options: ['est', 'cst', 'mst', 'pst'],
          description: 'Set your timezone'
        },
        {
          key: 'autoLogout',
          label: 'Auto Logout (minutes)',
          type: 'number',
          value: 30,
          description: 'Automatically log out after inactivity'
        }
      ]
    },
    {
      title: 'Security',
      description: 'Security and authentication settings',
      settings: [
        {
          key: 'twoFactorAuth',
          label: 'Two-Factor Authentication',
          type: 'toggle',
          value: false,
          description: 'Add an extra layer of security to your account'
        },
        {
          key: 'loginAlerts',
          label: 'Login Alerts',
          type: 'toggle',
          value: true,
          description: 'Get notified when someone logs into your account'
        }
      ]
    }
  ];

  updateSetting(sectionIndex: number, settingIndex: number, value: any): void {
    this.settingsSections[sectionIndex].settings[settingIndex].value = value;
    console.log('Setting updated:', {
      section: this.settingsSections[sectionIndex].title,
      setting: this.settingsSections[sectionIndex].settings[settingIndex].label,
      value: value
    });
  }

  saveSettings(): void {
    console.log('Saving all settings:', this.settingsSections);
    // Here you would typically send the settings to a backend service
  }

  resetSettings(): void {
    console.log('Resetting settings to defaults');
    // Here you would reset all settings to their default values
  }
}
