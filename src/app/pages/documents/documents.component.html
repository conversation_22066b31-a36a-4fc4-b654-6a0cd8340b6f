<div class="layout-content-container flex flex-col max-w-[960px] flex-1">
  <!-- <PERSON> Header -->
  <div class="flex flex-wrap justify-between gap-3 p-4">
    <div class="flex flex-col gap-1">
      <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">Documents</p>
      <p class="text-[#617789] text-base font-normal leading-normal">Access important building documents and forms</p>
    </div>
    <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#2094f3] text-white text-sm font-bold leading-normal tracking-[0.015em]">
      <span class="truncate">Upload Document</span>
    </button>
  </div>

  <!-- Category Filter -->
  <div class="flex gap-2 px-4 pb-4">
    <button
      *ngFor="let category of categories"
      (click)="selectCategory(category)"
      class="flex items-center justify-center rounded-full h-8 px-4 text-sm font-medium leading-normal"
      [class]="selectedCategory === category ? 'bg-[#2094f3] text-white' : 'bg-[#f0f2f4] text-[#111518]'"
    >
      {{ category }}
    </button>
  </div>

  <!-- Search Bar -->
  <div class="px-4 pb-4">
    <div class="flex w-full max-w-md items-stretch rounded-xl h-10">
      <div class="text-[#617789] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0">
        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
          <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
        </svg>
      </div>
      <input
        placeholder="Search documents..."
        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-full placeholder:text-[#617789] px-4 rounded-l-none border-l-0 pl-2 text-sm font-normal leading-normal"
      />
    </div>
  </div>

  <!-- Documents Grid -->
  <div class="p-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        *ngFor="let document of filteredDocuments"
        class="flex flex-col gap-3 rounded-xl border border-[#dbe1e6] p-4 hover:bg-[#f8f9fa] cursor-pointer"
      >
        <div class="flex items-center gap-3">
          <div class="text-2xl">{{ getFileIcon(document.type) }}</div>
          <div class="flex flex-col gap-1 flex-1">
            <h3 class="text-[#111518] text-base font-bold leading-tight truncate">{{ document.name }}</h3>
            <div class="flex items-center gap-2 text-xs text-[#617789]">
              <span class="px-2 py-1 rounded bg-[#f0f2f4]">{{ document.type }}</span>
              <span>{{ document.size }}</span>
            </div>
          </div>
        </div>

        <div class="flex flex-col gap-1 text-sm text-[#617789]">
          <div class="flex justify-between">
            <span>Category:</span>
            <span class="font-medium">{{ document.category }}</span>
          </div>
          <div class="flex justify-between">
            <span>Uploaded by:</span>
            <span class="font-medium">{{ document.uploadedBy }}</span>
          </div>
          <div class="flex justify-between">
            <span>Date:</span>
            <span class="font-medium">{{ document.uploadedDate | date:'shortDate' }}</span>
          </div>
        </div>

        <div class="flex gap-2 pt-2">
          <button class="flex-1 flex items-center justify-center rounded-lg h-8 px-3 bg-[#2094f3] text-white text-sm font-medium leading-normal">
            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256" class="mr-2">
              <path d="M224,152v56a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V152a8,8,0,0,1,16,0v56H208V152a8,8,0,0,1,16,0ZM101.66,122.34a8,8,0,0,0,11.31,11.32L128,118.63V184a8,8,0,0,0,16,0V118.63l15,15a8,8,0,0,0,11.31-11.32l-28.28-28.28a8,8,0,0,0-11.31,0Z"></path>
            </svg>
            Download
          </button>
          <button class="flex items-center justify-center rounded-lg h-8 px-3 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredDocuments.length === 0" class="flex flex-col items-center justify-center py-12">
    <div class="text-6xl mb-4">📄</div>
    <h3 class="text-[#111518] text-lg font-bold mb-2">No documents found</h3>
    <p class="text-[#617789] text-sm">Try adjusting your search or category filter</p>
  </div>
</div>
