import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Document } from '../../models/dashboard.models';

@Component({
  selector: 'app-documents',
  imports: [CommonModule],
  templateUrl: './documents.component.html',
  styleUrl: './documents.component.css'
})
export class DocumentsComponent {
  documents: Document[] = [
    {
      id: 'DOC001',
      name: 'Building Rules and Regulations',
      type: 'PDF',
      size: '2.4 MB',
      uploadedBy: 'Property Manager',
      uploadedDate: new Date('2024-01-01'),
      category: 'Policies',
      url: '#'
    },
    {
      id: 'DOC002',
      name: 'Emergency Procedures Guide',
      type: 'PDF',
      size: '1.8 MB',
      uploadedBy: 'Safety Committee',
      uploadedDate: new Date('2024-01-05'),
      category: 'Safety',
      url: '#'
    },
    {
      id: 'DOC003',
      name: 'Maintenance Request Form',
      type: 'PDF',
      size: '156 KB',
      uploadedBy: 'Maintenance Team',
      uploadedDate: new Date('2024-01-10'),
      category: 'Forms',
      url: '#'
    },
    {
      id: 'DOC004',
      name: 'Community Room Booking Form',
      type: 'PDF',
      size: '234 KB',
      uploadedBy: 'Front Desk',
      uploadedDate: new Date('2024-01-12'),
      category: 'Forms',
      url: '#'
    },
    {
      id: 'DOC005',
      name: 'Monthly Newsletter - January',
      type: 'PDF',
      size: '3.2 MB',
      uploadedBy: 'Communications Team',
      uploadedDate: new Date('2024-01-15'),
      category: 'Newsletter',
      url: '#'
    },
    {
      id: 'DOC006',
      name: 'Parking Guidelines',
      type: 'PDF',
      size: '892 KB',
      uploadedBy: 'Property Manager',
      uploadedDate: new Date('2024-01-08'),
      category: 'Policies',
      url: '#'
    }
  ];

  categories = ['All', 'Policies', 'Safety', 'Forms', 'Newsletter', 'Legal'];
  selectedCategory = 'All';

  get filteredDocuments(): Document[] {
    if (this.selectedCategory === 'All') {
      return this.documents;
    }
    return this.documents.filter(doc => doc.category === this.selectedCategory);
  }

  selectCategory(category: string): void {
    this.selectedCategory = category;
  }

  getFileIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'pdf': return '📄';
      case 'doc':
      case 'docx': return '📝';
      case 'xls':
      case 'xlsx': return '📊';
      case 'ppt':
      case 'pptx': return '📋';
      default: return '📄';
    }
  }
}
