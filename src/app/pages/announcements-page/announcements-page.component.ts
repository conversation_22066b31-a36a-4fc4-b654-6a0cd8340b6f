import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Announcement } from '../../models/dashboard.models';

@Component({
  selector: 'app-announcements-page',
  imports: [CommonModule],
  templateUrl: './announcements-page.component.html',
  styleUrl: './announcements-page.component.css'
})
export class AnnouncementsPageComponent {
  announcements: Announcement[] = [
    {
      id: '1',
      title: 'Important Update: Building Maintenance Schedule',
      content: 'Please review the updated maintenance schedule for the building. There will be a temporary water shut-off on July 10th from 9 AM to 12 PM for essential repairs. We apologize for any inconvenience this may cause. Please ensure you have adequate water stored for this period.',
      isNew: true
    },
    {
      id: '2',
      title: 'New Gym Equipment Installation',
      content: 'We are excited to announce that new fitness equipment will be installed in the gym this weekend. The gym will be temporarily closed from Saturday 8 AM to Sunday 6 PM. Thank you for your patience as we upgrade our facilities.',
      isNew: true
    },
    {
      id: '3',
      title: 'Holiday Pool Hours',
      content: 'During the holiday season, the pool will have extended hours. Starting December 20th, the pool will be open from 6 AM to 11 PM daily. Regular hours will resume on January 2nd.',
      isNew: false
    },
    {
      id: '4',
      title: 'Parking Garage Cleaning',
      content: 'The parking garage will undergo deep cleaning and maintenance on the first Sunday of each month from 6 AM to 2 PM. Please move your vehicles to the street parking during this time.',
      isNew: false
    },
    {
      id: '5',
      title: 'Community Garden Project',
      content: 'We are starting a community garden project on the rooftop! Interested residents can sign up at the front desk. The first planning meeting will be held in the community room on January 15th at 7 PM.',
      isNew: false
    }
  ];

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getCurrentDate(): string {
    return this.formatDate(new Date());
  }

  getRandomLikes(): number {
    return Math.floor(Math.random() * 50) + 10;
  }

  getRandomComments(): number {
    return Math.floor(Math.random() * 20) + 5;
  }

  getRandomShares(): number {
    return Math.floor(Math.random() * 15) + 2;
  }
}
