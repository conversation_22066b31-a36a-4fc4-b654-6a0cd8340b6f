<div class="layout-content-container flex flex-col max-w-[960px] flex-1">
  <!-- <PERSON> Header -->
  <div class="flex flex-wrap justify-between gap-3 p-4">
    <div class="flex flex-col gap-1">
      <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">Announcements</p>
      <p class="text-[#617789] text-base font-normal leading-normal">Stay updated with the latest building news and updates</p>
    </div>
    <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#2094f3] text-white text-sm font-bold leading-normal tracking-[0.015em]">
      <span class="truncate">Create Announcement</span>
    </button>
  </div>

  <!-- Filter Tabs -->
  <div class="flex gap-2 px-4 pb-4">
    <button class="flex items-center justify-center rounded-full h-8 px-4 bg-[#2094f3] text-white text-sm font-medium leading-normal">
      All
    </button>
    <button class="flex items-center justify-center rounded-full h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
      New
    </button>
    <button class="flex items-center justify-center rounded-full h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
      Important
    </button>
    <button class="flex items-center justify-center rounded-full h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
      Maintenance
    </button>
  </div>

  <!-- Announcements List -->
  <div class="p-4">
    <div class="flex flex-col gap-6">
      <div
        *ngFor="let announcement of announcements"
        class="flex flex-col gap-4 rounded-xl border border-[#dbe1e6] p-6"
      >
        <div class="flex items-start justify-between gap-4">
          <div class="flex flex-col gap-2 flex-1">
            <div class="flex items-center gap-3">
              <span
                *ngIf="announcement.isNew"
                class="px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100"
              >
                New
              </span>
              <span class="text-[#617789] text-sm font-normal">{{ getCurrentDate() }}</span>
            </div>
            <h3 class="text-[#111518] text-xl font-bold leading-tight">{{ announcement.title }}</h3>
            <p class="text-[#617789] text-base font-normal leading-normal">{{ announcement.content }}</p>
          </div>
          <div class="flex flex-col gap-2">
            <button class="flex items-center justify-center rounded-xl h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
              <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256" class="mr-2">
                <path d="M178,32c-20.65,0-38.73,8.88-50,23.89C116.73,40.88,98.65,32,78,32A62.07,62.07,0,0,0,16,94c0,70,103.79,126.66,108.21,129a8,8,0,0,0,7.58,0C136.21,220.66,240,164,240,94A62.07,62.07,0,0,0,178,32ZM128,206.8C109.74,196.16,32,147.69,32,94A46.06,46.06,0,0,1,78,48c19.45,0,35.78,10.36,42.6,27a8,8,0,0,0,14.8,0c6.82-16.67,23.15-27,42.6-27a46.06,46.06,0,0,1,46,46C224,147.69,146.26,196.16,128,206.8Z"></path>
              </svg>
              Like
            </button>
            <button class="flex items-center justify-center rounded-xl h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
              <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256" class="mr-2">
                <path d="M237.66,117.66l-80,80A8,8,0,0,1,144,192V152H64a8,8,0,0,1,0-16h80V96a8,8,0,0,1,13.66-5.66l80,80A8,8,0,0,1,237.66,117.66Z"></path>
              </svg>
              Share
            </button>
          </div>
        </div>

        <!-- Engagement Stats -->
        <div class="flex items-center gap-6 pt-4 border-t border-[#dbe1e6]">
          <div class="flex items-center gap-2 text-sm text-[#617789]">
            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M178,32c-20.65,0-38.73,8.88-50,23.89C116.73,40.88,98.65,32,78,32A62.07,62.07,0,0,0,16,94c0,70,103.79,126.66,108.21,129a8,8,0,0,0,7.58,0C136.21,220.66,240,164,240,94A62.07,62.07,0,0,0,178,32ZM128,206.8C109.74,196.16,32,147.69,32,94A46.06,46.06,0,0,1,78,48c19.45,0,35.78,10.36,42.6,27a8,8,0,0,0,14.8,0c6.82-16.67,23.15-27,42.6-27a46.06,46.06,0,0,1,46,46C224,147.69,146.26,196.16,128,206.8Z"></path>
            </svg>
            <span>{{ getRandomLikes() }} likes</span>
          </div>
          <div class="flex items-center gap-2 text-sm text-[#617789]">
            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M216,48H40A16,16,0,0,0,24,64V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V64A16,16,0,0,0,216,48ZM40,64H216V192H40Z"></path>
            </svg>
            <span>{{ getRandomComments() }} comments</span>
          </div>
          <div class="flex items-center gap-2 text-sm text-[#617789]">
            <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M237.66,117.66l-80,80A8,8,0,0,1,144,192V152H64a8,8,0,0,1,0-16h80V96a8,8,0,0,1,13.66-5.66l80,80A8,8,0,0,1,237.66,117.66Z"></path>
            </svg>
            <span>{{ getRandomShares() }} shares</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
