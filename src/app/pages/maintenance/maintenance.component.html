<div class="layout-content-container flex flex-col w-full flex-1">
  <!-- Page Header -->
  <div class="flex flex-col sm:flex-row sm:justify-between gap-3 p-4">
    <div class="flex flex-col gap-1">
      <p class="text-[#111518] tracking-light text-2xl md:text-[32px] font-bold leading-tight">Maintenance</p>
      <p class="text-[#617789] text-sm md:text-base font-normal leading-normal">Manage maintenance requests and track progress</p>
    </div>
    <button class="flex w-full sm:w-auto min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#2094f3] text-white text-sm font-bold leading-normal tracking-[0.015em]">
      <span class="truncate">New Request</span>
    </button>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
    <div class="flex flex-col gap-2 rounded-xl p-4 md:p-6 border border-[#dbe1e6]">
      <p class="text-[#111518] text-sm md:text-base font-medium leading-normal">Open Requests</p>
      <p class="text-[#111518] tracking-light text-xl md:text-2xl font-bold leading-tight">{{ getOpenRequestsCount() }}</p>
    </div>
    <div class="flex flex-col gap-2 rounded-xl p-4 md:p-6 border border-[#dbe1e6]">
      <p class="text-[#111518] text-sm md:text-base font-medium leading-normal">In Progress</p>
      <p class="text-[#111518] tracking-light text-xl md:text-2xl font-bold leading-tight">{{ getInProgressRequestsCount() }}</p>
    </div>
    <div class="flex flex-col gap-2 rounded-xl p-4 md:p-6 border border-[#dbe1e6]">
      <p class="text-[#111518] text-sm md:text-base font-medium leading-normal">Completed This Month</p>
      <p class="text-[#111518] tracking-light text-xl md:text-2xl font-bold leading-tight">{{ getCompletedRequestsCount() }}</p>
    </div>
  </div>

  <!-- Maintenance Requests List -->
  <h2 class="text-[#111518] text-lg md:text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Recent Requests</h2>
  <div class="p-4">
    <div class="flex flex-col gap-4">
      <div
        *ngFor="let request of maintenanceRequests"
        class="flex flex-col lg:flex-row lg:items-center gap-4 rounded-xl border border-[#dbe1e6] p-4"
      >
        <div class="flex flex-col gap-2 flex-1">
          <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
            <h3 class="text-[#111518] text-base md:text-lg font-bold leading-tight">{{ request.title }}</h3>
            <div class="flex gap-2">
              <span class="px-2 py-1 rounded-full text-xs font-medium" [class]="getPriorityColor(request.priority)">
                {{ request.priority }}
              </span>
              <span class="px-2 py-1 rounded-full text-xs font-medium" [class]="getStatusColor(request.status)">
                {{ request.status }}
              </span>
            </div>
          </div>
          <p class="text-[#617789] text-sm font-normal leading-normal">{{ request.description }}</p>
          <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-[#617789]">
            <span><strong>ID:</strong> {{ request.id }}</span>
            <span><strong>Category:</strong> {{ request.category }}</span>
            <span><strong>Requested by:</strong> {{ request.requestedBy }}</span>
            <span *ngIf="request.assignedTo"><strong>Assigned to:</strong> {{ request.assignedTo }}</span>
          </div>
          <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-[#617789]">
            <span><strong>Created:</strong> {{ request.createdDate | date:'short' }}</span>
            <span *ngIf="request.dueDate"><strong>Due:</strong> {{ request.dueDate | date:'short' }}</span>
          </div>
        </div>
        <div class="flex flex-row lg:flex-col gap-2 w-full lg:w-auto">
          <button class="flex-1 lg:flex-none flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
            View Details
          </button>
          <button class="flex-1 lg:flex-none flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-8 px-4 bg-[#2094f3] text-white text-sm font-medium leading-normal">
            Update Status
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
