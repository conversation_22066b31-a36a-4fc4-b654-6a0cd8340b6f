import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaintenanceRequest } from '../../models/dashboard.models';

@Component({
  selector: 'app-maintenance',
  imports: [CommonModule],
  templateUrl: './maintenance.component.html',
  styleUrl: './maintenance.component.css'
})
export class MaintenanceComponent {
  maintenanceRequests: MaintenanceRequest[] = [
    {
      id: 'MR001',
      title: 'Leaky Faucet in Unit 3B',
      description: 'Kitchen faucet has been dripping for 3 days. Needs immediate attention.',
      priority: 'High',
      status: 'Open',
      requestedBy: '<PERSON>',
      assignedTo: '<PERSON>',
      createdDate: new Date('2024-01-15'),
      dueDate: new Date('2024-01-17'),
      category: 'Plumbing'
    },
    {
      id: 'MR002',
      title: 'Elevator Maintenance',
      description: 'Monthly elevator inspection and maintenance required.',
      priority: 'Medium',
      status: 'In Progress',
      requestedBy: 'Property Manager',
      assignedTo: 'Elevator Tech Inc.',
      createdDate: new Date('2024-01-10'),
      dueDate: new Date('2024-01-20'),
      category: 'Elevator'
    },
    {
      id: 'MR003',
      title: 'HVAC Filter Replacement',
      description: 'Replace air filters in common areas and units 1A-1F.',
      priority: 'Low',
      status: 'Completed',
      requestedBy: 'Maintenance Team',
      assignedTo: 'HVAC Specialists',
      createdDate: new Date('2024-01-05'),
      dueDate: new Date('2024-01-15'),
      category: 'HVAC'
    }
  ];

  getPriorityColor(priority: string): string {
    switch (priority) {
      case 'Critical': return 'text-red-600 bg-red-100';
      case 'High': return 'text-orange-600 bg-orange-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'Low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'Open': return 'text-blue-600 bg-blue-100';
      case 'In Progress': return 'text-purple-600 bg-purple-100';
      case 'Completed': return 'text-green-600 bg-green-100';
      case 'Cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  getOpenRequestsCount(): number {
    return this.maintenanceRequests.filter(req => req.status === 'Open').length;
  }

  getInProgressRequestsCount(): number {
    return this.maintenanceRequests.filter(req => req.status === 'In Progress').length;
  }

  getCompletedRequestsCount(): number {
    return this.maintenanceRequests.filter(req => req.status === 'Completed').length;
  }
}
