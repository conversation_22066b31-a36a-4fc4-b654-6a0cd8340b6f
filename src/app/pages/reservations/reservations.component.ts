import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Reservation } from '../../models/dashboard.models';

@Component({
  selector: 'app-reservations',
  imports: [CommonModule],
  templateUrl: './reservations.component.html',
  styleUrl: './reservations.component.css'
})
export class ReservationsComponent {
  reservations: Reservation[] = [
    {
      id: 'RES001',
      facilityName: 'Community Room',
      reservedBy: '<PERSON>',
      startTime: new Date('2024-01-20T14:00:00'),
      endTime: new Date('2024-01-20T17:00:00'),
      status: 'Confirmed',
      purpose: 'Birthday Party',
      attendees: 25
    },
    {
      id: 'RES002',
      facilityName: 'Gym',
      reservedBy: '<PERSON>',
      startTime: new Date('2024-01-22T09:00:00'),
      endTime: new Date('2024-01-22T11:00:00'),
      status: 'Confirmed',
      purpose: 'Personal Training Session',
      attendees: 3
    },
    {
      id: 'RES003',
      facilityName: 'Rooftop Terrace',
      reservedBy: '<PERSON>',
      startTime: new Date('2024-01-25T18:00:00'),
      endTime: new Date('2024-01-25T22:00:00'),
      status: 'Pending',
      purpose: 'Corporate Event',
      attendees: 50
    },
    {
      id: 'RES004',
      facilityName: 'Pool Area',
      reservedBy: 'David Wilson',
      startTime: new Date('2024-01-18T10:00:00'),
      endTime: new Date('2024-01-18T14:00:00'),
      status: 'Cancelled',
      purpose: 'Swimming Lessons',
      attendees: 8
    }
  ];

  getStatusColor(status: string): string {
    switch (status) {
      case 'Confirmed': return 'text-green-600 bg-green-100';
      case 'Pending': return 'text-yellow-600 bg-yellow-100';
      case 'Cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  formatTimeRange(startTime: Date, endTime: Date): string {
    const start = startTime.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
    const end = endTime.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
    return `${start} - ${end}`;
  }
}
