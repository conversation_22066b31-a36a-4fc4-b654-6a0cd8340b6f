<div class="layout-content-container flex flex-col max-w-[960px] flex-1">
  <!-- <PERSON> Header -->
  <div class="flex flex-wrap justify-between gap-3 p-4">
    <div class="flex flex-col gap-1">
      <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">Reservations</p>
      <p class="text-[#617789] text-base font-normal leading-normal">Book and manage facility reservations</p>
    </div>
    <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#2094f3] text-white text-sm font-bold leading-normal tracking-[0.015em]">
      <span class="truncate">New Reservation</span>
    </button>
  </div>

  <!-- Available Facilities -->
  <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Available Facilities</h2>
  <div class="flex flex-wrap gap-4 p-4">
    <div class="flex min-w-[200px] flex-1 flex-col gap-2 rounded-xl p-4 border border-[#dbe1e6] hover:bg-[#f8f9fa] cursor-pointer">
      <h3 class="text-[#111518] text-lg font-bold leading-tight">Community Room</h3>
      <p class="text-[#617789] text-sm font-normal leading-normal">Perfect for parties and meetings</p>
      <p class="text-[#111518] text-sm font-medium">Capacity: 50 people</p>
    </div>
    <div class="flex min-w-[200px] flex-1 flex-col gap-2 rounded-xl p-4 border border-[#dbe1e6] hover:bg-[#f8f9fa] cursor-pointer">
      <h3 class="text-[#111518] text-lg font-bold leading-tight">Gym</h3>
      <p class="text-[#617789] text-sm font-normal leading-normal">Fully equipped fitness center</p>
      <p class="text-[#111518] text-sm font-medium">Capacity: 15 people</p>
    </div>
    <div class="flex min-w-[200px] flex-1 flex-col gap-2 rounded-xl p-4 border border-[#dbe1e6] hover:bg-[#f8f9fa] cursor-pointer">
      <h3 class="text-[#111518] text-lg font-bold leading-tight">Rooftop Terrace</h3>
      <p class="text-[#617789] text-sm font-normal leading-normal">Outdoor space with city views</p>
      <p class="text-[#111518] text-sm font-medium">Capacity: 75 people</p>
    </div>
    <div class="flex min-w-[200px] flex-1 flex-col gap-2 rounded-xl p-4 border border-[#dbe1e6] hover:bg-[#f8f9fa] cursor-pointer">
      <h3 class="text-[#111518] text-lg font-bold leading-tight">Pool Area</h3>
      <p class="text-[#617789] text-sm font-normal leading-normal">Swimming pool and lounge area</p>
      <p class="text-[#111518] text-sm font-medium">Capacity: 30 people</p>
    </div>
  </div>

  <!-- Current Reservations -->
  <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Current Reservations</h2>
  <div class="p-4">
    <div class="flex flex-col gap-4">
      <div
        *ngFor="let reservation of reservations"
        class="flex items-center justify-between gap-4 rounded-xl border border-[#dbe1e6] p-4"
      >
        <div class="flex flex-col gap-2 flex-1">
          <div class="flex items-center gap-3">
            <h3 class="text-[#111518] text-lg font-bold leading-tight">{{ reservation.facilityName }}</h3>
            <span class="px-2 py-1 rounded-full text-xs font-medium" [class]="getStatusColor(reservation.status)">
              {{ reservation.status }}
            </span>
          </div>
          <p class="text-[#617789] text-sm font-normal leading-normal"><strong>Purpose:</strong> {{ reservation.purpose }}</p>
          <div class="flex items-center gap-4 text-sm text-[#617789]">
            <span><strong>Reserved by:</strong> {{ reservation.reservedBy }}</span>
            <span><strong>Attendees:</strong> {{ reservation.attendees }}</span>
          </div>
          <div class="flex items-center gap-4 text-sm text-[#617789]">
            <span><strong>Date:</strong> {{ reservation.startTime | date:'fullDate' }}</span>
            <span><strong>Time:</strong> {{ formatTimeRange(reservation.startTime, reservation.endTime) }}</span>
          </div>
        </div>
        <div class="flex flex-col gap-2">
          <button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-8 px-4 bg-[#f0f2f4] text-[#111518] text-sm font-medium leading-normal">
            View Details
          </button>
          <button
            *ngIf="reservation.status !== 'Cancelled'"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-8 px-4 bg-[#dc3545] text-white text-sm font-medium leading-normal"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
