import { Injectable, inject } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';

@Injectable({
  providedIn: 'root'
})
export class KeycloakInitService {
  private keycloakService = inject(KeycloakService);
  private initialized = false;

  async waitForInitialization(): Promise<boolean> {
    if (this.initialized) {
      return this.keycloakService.isLoggedIn();
    }

    // Wait for Keycloak to be properly initialized
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait
    
    while (attempts < maxAttempts) {
      try {
        // Try to access Keycloak service
        const isLoggedIn = this.keycloakService.isLoggedIn();
        this.initialized = true;
        return isLoggedIn;
      } catch (error) {
        // Keycloak not ready yet, wait a bit more
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
    }

    // If we get here, Keycloak failed to initialize
    console.error('Keycloak failed to initialize after 5 seconds');
    this.initialized = true;
    return false;
  }

  isInitialized(): boolean {
    return this.initialized;
  }
}
