import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, from, of, tap, map, catchError } from 'rxjs';
import { KeycloakService } from 'keycloak-angular';

export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private keycloakService = inject(KeycloakService);
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor() {
    this.initializeUserState();
  }

  /**
   * Initialize user state from Keycloak
   */
  private initializeUserState(): void {
    try {
      const isLoggedIn = this.keycloakService.isLoggedIn();
      if (isLoggedIn) {
        this.updateUserState();
      }
    } catch (error) {
      console.error('Error initializing user state:', error);
    }
  }

  /**
   * Update user state from Keycloak token
   */
  private updateUserState(): void {
    try {
      const isLoggedIn = this.keycloakService.isLoggedIn();
      if (isLoggedIn) {
        // Get user profile from Keycloak
        this.keycloakService.loadUserProfile().then(profile => {
          const user: User = {
            id: profile.id || 'unknown',
            username: profile.username || profile.email || 'user',
            email: profile.email || '',
            firstName: profile.firstName,
            lastName: profile.lastName
          };

          this.currentUserSubject.next(user);
          this.isAuthenticatedSubject.next(true);
        }).catch(error => {
          console.error('Error loading user profile:', error);
          // Fallback user object
          const user: User = {
            id: 'user-id',
            username: 'user',
            email: '<EMAIL>',
            firstName: 'User',
            lastName: 'Name'
          };
          this.currentUserSubject.next(user);
          this.isAuthenticatedSubject.next(true);
        });
      } else {
        this.currentUserSubject.next(null);
        this.isAuthenticatedSubject.next(false);
      }
    } catch (error) {
      console.error('Error updating user state:', error);
      this.currentUserSubject.next(null);
      this.isAuthenticatedSubject.next(false);
    }
  }

  /**
   * Check authentication status from Keycloak
   */
  checkAuthStatus(): Observable<boolean> {
    try {
      const isLoggedIn = this.keycloakService.isLoggedIn();
      if (isLoggedIn) {
        this.updateUserState();
      }
      return of(isLoggedIn);
    } catch (error) {
      console.error('Error checking auth status:', error);
      this.updateUserState();
      return of(false);
    }
  }

  /**
   * Get current user information
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    try {
      return this.keycloakService.isLoggedIn();
    } catch (error) {
      console.error('Error checking authentication:', error);
      return false;
    }
  }

  /**
   * Get user roles
   */
  getUserRoles(): string[] {
    try {
      const isLoggedIn = this.keycloakService.isLoggedIn();
      if (!isLoggedIn) {
        return [];
      }
      return this.keycloakService.getUserRoles();
    } catch (error) {
      console.error('Error getting user roles:', error);
      return [];
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    try {
      return this.keycloakService.isUserInRole(role);
    } catch (error) {
      console.error('Error checking user role:', error);
      return false;
    }
  }

  /**
   * Logout user through Keycloak
   */
  logout(): Observable<any> {
    return from(this.keycloakService.logout(window.location.origin)).pipe(
      tap(() => {
        this.currentUserSubject.next(null);
        this.isAuthenticatedSubject.next(false);
      })
    );
  }

  /**
   * Initiate login through Keycloak
   */
  login(): void {
    this.keycloakService.login({
      redirectUri: window.location.origin + '/dashboard'
    });
  }

  /**
   * Get access token
   */
  async getToken(): Promise<string> {
    try {
      return await this.keycloakService.getToken();
    } catch (error) {
      console.error('Error getting token:', error);
      return '';
    }
  }
}
