import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, from, of, tap, map, catchError } from 'rxjs';
import { KeycloakService } from 'keycloak-angular';

export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private keycloak = inject(Keycloak) as Keycloak;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor() {
    this.initializeUserState();
  }

  /**
   * Initialize user state from Keycloak
   */
  private initializeUserState(): void {
    if (this.keycloak.authenticated) {
      this.updateUserState();
    }
  }

  /**
   * Update user state from Keycloak token
   */
  private updateUserState(): void {
    if (this.keycloak.authenticated && this.keycloak.tokenParsed) {
      const token = this.keycloak.tokenParsed as any;
      const user: User = {
        id: token.sub,
        username: token.preferred_username || token.sub,
        email: token.email || '',
        firstName: token.given_name,
        lastName: token.family_name
      };

      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
    } else {
      this.currentUserSubject.next(null);
      this.isAuthenticatedSubject.next(false);
    }
  }

  /**
   * Check authentication status from Keycloak
   */
  checkAuthStatus(): Observable<boolean> {
    return from(this.keycloak.updateToken(30)).pipe(
      tap(() => this.updateUserState()),
      map(() => this.keycloak.authenticated || false)
    ).pipe(
      catchError(() => {
        this.updateUserState();
        return of(this.keycloak.authenticated || false);
      })
    );
  }

  /**
   * Get current user information
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    return this.keycloak.authenticated || false;
  }

  /**
   * Get user roles
   */
  getUserRoles(): string[] {
    if (!this.keycloak.authenticated) {
      return [];
    }
    return this.keycloak.realmAccess?.roles || [];
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    return this.getUserRoles().includes(role);
  }

  /**
   * Logout user through Keycloak
   */
  logout(): Observable<any> {
    return from(this.keycloak.logout({
      redirectUri: window.location.origin
    })).pipe(
      tap(() => {
        this.currentUserSubject.next(null);
        this.isAuthenticatedSubject.next(false);
      })
    );
  }

  /**
   * Initiate login through Keycloak
   */
  login(): void {
    this.keycloak.login({
      redirectUri: window.location.origin + '/dashboard'
    });
  }

  /**
   * Get access token
   */
  getToken(): string | undefined {
    return this.keycloak.token;
  }
}
