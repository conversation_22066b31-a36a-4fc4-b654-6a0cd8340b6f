import { Component, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { HeaderComponent } from './components/header/header.component';
import { MobileNavComponent } from './components/mobile-nav/mobile-nav.component';
import { MobileSidebarComponent } from './components/mobile-sidebar/mobile-sidebar.component';
import { AuthService } from './services/auth.service';
import Keycloak from 'keycloak-js';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    SidebarComponent,
    HeaderComponent,
    MobileNavComponent,
    MobileSidebarComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit {
  private authService = inject(AuthService);
  private keycloak = inject(Keycloak) as Keycloak;

  title = 'PropertyPro Dashboard';
  isMobileSidebarOpen = false;
  isAuthInitialized = false;
  isAuthenticated = false;

  ngOnInit() {
    // Wait for Keycloak to be fully initialized
    this.checkAuthenticationStatus();
  }

  private checkAuthenticationStatus() {
    // Check if Keycloak is already initialized
    if (this.keycloak.authenticated !== undefined) {
      this.isAuthenticated = this.keycloak.authenticated;
      this.isAuthInitialized = true;
    } else {
      // If not initialized yet, wait a bit and check again
      setTimeout(() => this.checkAuthenticationStatus(), 100);
    }
  }

  toggleMobileSidebar() {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
  }

  closeMobileSidebar() {
    this.isMobileSidebarOpen = false;
  }
}
