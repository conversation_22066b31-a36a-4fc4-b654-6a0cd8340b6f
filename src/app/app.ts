import { Component, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { HeaderComponent } from './components/header/header.component';
import { MobileNavComponent } from './components/mobile-nav/mobile-nav.component';
import { MobileSidebarComponent } from './components/mobile-sidebar/mobile-sidebar.component';
import { LoadingComponent } from './components/loading/loading.component';
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    SidebarComponent,
    HeaderComponent,
    MobileNavComponent,
    MobileSidebarComponent,
    LoadingComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit {
  private keycloakService = inject(KeycloakService);

  title = 'PropertyPro Dashboard';
  isMobileSidebarOpen = false;
  isAuthInitialized = false;
  isAuthenticated = false;

  async ngOnInit() {
    // Wait for Keycloak to be fully initialized
    await this.checkAuthenticationStatus();
  }

  private async checkAuthenticationStatus() {
    try {
      // Wait a moment for Keycloak to initialize after redirect
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if Keycloak is ready
      this.isAuthenticated = this.keycloakService.isLoggedIn();
      this.isAuthInitialized = true;

      console.log('Auth status:', {
        isAuthenticated: this.isAuthenticated,
        isInitialized: this.isAuthInitialized
      });
    } catch (error) {
      console.error('Error checking authentication status:', error);
      this.isAuthenticated = false;
      this.isAuthInitialized = true;
    }
  }

  toggleMobileSidebar() {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
  }

  closeMobileSidebar() {
    this.isMobileSidebarOpen = false;
  }
}
