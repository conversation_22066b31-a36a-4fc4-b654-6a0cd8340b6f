import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import Keycloak from 'keycloak-js';

export const authGuard: CanActivateFn = (route, state) => {
  const keycloak = inject(Keycloak) as Keycloak;

  if (keycloak.authenticated) {
    return true;
  } else {
    // Redirect to Keycloak login
    keycloak.login({
      redirectUri: window.location.origin + state.url
    });
    return false;
  }
};
