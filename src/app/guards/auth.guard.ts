import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

export const authGuard: CanActivateFn = (route, state) => {
  const keycloakService = inject(KeycloakService);

  try {
    const isLoggedIn = keycloakService.isLoggedIn();

    if (isLoggedIn) {
      return true;
    } else {
      // Redirect to Keycloak login
      keycloakService.login({
        redirectUri: window.location.origin + state.url
      });
      return false;
    }
  } catch (error) {
    console.error('Error in auth guard:', error);
    console.log('Keycloak might not be available, redirecting to login');
    // If there's an error, redirect to login
    keycloakService.login({
      redirectUri: window.location.origin + state.url
    });
    return false;
  }
};
