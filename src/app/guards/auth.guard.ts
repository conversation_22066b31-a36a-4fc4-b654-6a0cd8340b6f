import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

export const authGuard: CanActivateFn = async (route, state) => {
  const keycloakService = inject(KeycloakService);

  try {
    const isLoggedIn = await keycloakService.isLoggedIn();

    if (isLoggedIn) {
      return true;
    } else {
      // Redirect to Keycloak login
      await keycloakService.login({
        redirectUri: window.location.origin + state.url
      });
      return false;
    }
  } catch (error) {
    console.error('Error in auth guard:', error);
    // If there's an error, redirect to login
    await keycloakService.login({
      redirectUri: window.location.origin + state.url
    });
    return false;
  }
};
