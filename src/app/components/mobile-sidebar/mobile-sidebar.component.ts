import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MenuItem } from '../../models/dashboard.models';

@Component({
  selector: 'app-mobile-sidebar',
  imports: [CommonModule, RouterModule],
  templateUrl: './mobile-sidebar.component.html',
  styleUrl: './mobile-sidebar.component.css'
})
export class MobileSidebarComponent {
  @Input() isOpen = false;
  @Output() close = new EventEmitter<void>();

  menuItems: MenuItem[] = [
    { icon: 'House', label: 'Dashboard', route: '/dashboard' },
    { icon: 'Wrench', label: 'Maintenance', route: '/maintenance' },
    { icon: 'Calendar', label: 'Reservations', route: '/reservations' },
    { icon: 'Megaphone', label: 'Announcements', route: '/announcements' },
    { icon: 'File', label: 'Documents', route: '/documents' },
    { icon: 'Gear', label: 'Settings', route: '/settings' }
  ];

  constructor(private router: Router) {}

  isActiveRoute(route: string): boolean {
    return this.router.url === route;
  }

  onClose() {
    this.close.emit();
  }

  onMenuItemClick() {
    this.close.emit();
  }
}
