import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MenuItem } from '../../models/dashboard.models';

@Component({
  selector: 'app-sidebar',
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent {
  menuItems: MenuItem[] = [
    { icon: 'House', label: 'Dashboard', route: '/dashboard' },
    { icon: 'Wrench', label: 'Maintenance', route: '/maintenance' },
    { icon: 'Calendar', label: 'Reservations', route: '/reservations' },
    { icon: 'Megaphone', label: 'Announcements', route: '/announcements' },
    { icon: 'File', label: 'Documents', route: '/documents' },
    { icon: 'Gear', label: 'Settings', route: '/settings' }
  ];

  constructor(private router: Router) {}

  isActiveRoute(route: string): boolean {
    return this.router.url === route;
  }
}
