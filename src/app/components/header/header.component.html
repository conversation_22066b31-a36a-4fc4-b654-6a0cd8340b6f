<header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-4 md:px-10 py-3">
  <div class="flex items-center gap-4 text-[#111518]">
    <!-- Mobile Menu Button -->
    <button
      (click)="onToggleSidebar()"
      class="md:hidden flex items-center justify-center w-8 h-8 rounded-lg hover:bg-[#f0f2f4] transition-colors"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
        <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,72H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM216,184H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Z"></path>
      </svg>
    </button>

    <!-- Logo -->
    <div class="size-4">
      <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z" fill="currentColor"></path>
      </svg>
    </div>
    <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em] hidden sm:block">
      PropertyPro
    </h2>
  </div>

  <div class="flex flex-1 justify-end gap-2 md:gap-8 items-center">
    <!-- Search Bar -->
    <label class="flex flex-col min-w-32 md:min-w-40 !h-10 max-w-64">
      <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
        <div
          class="text-[#617789] flex border-none bg-[#f0f2f4] items-center justify-center pl-3 md:pl-4 rounded-l-xl border-r-0"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
            <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
          </svg>
        </div>
        <input
          placeholder="Search"
          class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-full placeholder:text-[#617789] px-2 md:px-4 rounded-l-none border-l-0 pl-2 text-sm md:text-base font-normal leading-normal"
          [(ngModel)]="searchQuery"
        />
      </div>
    </label>

    <!-- User Menu -->
    <div class="relative" *ngIf="isAuthenticated$ | async">
      <div
        class="group inline-block relative"
        tabindex="0"
      >
        <!-- Avatar Button -->
        <button
          class="flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 w-10 bg-[#f0f2f4] text-[#111518] text-sm font-bold leading-normal tracking-[0.015em]"
        >
          <ng-container *ngIf="currentUser$ | async as user; else fallbackIcon">
            {{ getUserInitials(user) }}
          </ng-container>
          <ng-template #fallbackIcon>
            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
            </svg>
          </ng-template>
        </button>

        <!-- Dropdown -->
        <!-- Toggle Button -->
        <button
          (click)="toggleDropdown()"
          class="relative z-50 px-4 py-2 bg-gray-200 rounded"
        >
          Menu
        </button>

        <!-- Dropdown -->
        <div
          *ngIf="isDropdownOpen"
          class="absolute right-0 mt-2 w-44 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50 transition-opacity duration-150"
        >
          <div
            class="px-4 py-2 text-sm text-[#111518] font-semibold border-b border-gray-200"
            *ngIf="currentUser$ | async as user"
          >
            {{ getUserDisplayName(user) }}
          </div>
          <button
            (click)="logout()"
            class="w-full text-left px-4 py-2 text-sm text-[#111518] hover:bg-gray-100"
          >
            Log out
          </button>
        </div>

      </div>
    </div>
  </div>
</header>
