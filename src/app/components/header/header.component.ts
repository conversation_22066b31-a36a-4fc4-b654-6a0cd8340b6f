import {Component, EventEmitter, inject, Output} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {Router, RouterModule} from '@angular/router';
import {AuthService, User} from '../../services/auth.service';
import {Observable} from 'rxjs';

@Component({
  selector: 'app-header',
  imports: [CommonModule, FormsModule,RouterModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {

  @Output() toggleSidebar = new EventEmitter<void>();

  searchQuery: string = '';
  isDropdownOpen = false;

  private authService = inject(AuthService);
  private router = inject(Router);

  currentUser$: Observable<User | null>;
  isAuthenticated$: Observable<boolean>;

  constructor() {
    this.currentUser$ = this.authService.currentUser$;
    this.isAuthenticated$ = this.authService.isAuthenticated$;
  }

  onToggleSidebar() {
    this.toggleSidebar.emit();
  }

  getUserDisplayName(user: User): string {
    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    if (user.firstName) {
      return user.firstName;
    }
    return user.username;
  }

  getUserInitials(user: User): string {
    if (user.firstName && user.lastName) {
      return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
    }
    if (user.firstName) {
      return user.firstName.charAt(0).toUpperCase();
    }
    return user.username.charAt(0).toUpperCase();
  }

  login(): void {
    this.authService.login();
  }

  logout(): void {
    this.authService.logout().subscribe();
  }
}
