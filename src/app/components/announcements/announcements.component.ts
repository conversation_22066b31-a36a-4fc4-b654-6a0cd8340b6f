import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Announcement } from '../../models/dashboard.models';

@Component({
  selector: 'app-announcements',
  imports: [CommonModule],
  templateUrl: './announcements.component.html',
  styleUrl: './announcements.component.css'
})
export class AnnouncementsComponent {
  announcements: Announcement[] = [
    {
      id: '1',
      title: 'Important Update: Building Maintenance Schedule',
      content: 'Please review the updated maintenance schedule for the building. There will be a temporary water shut-off on July 10th from 9 AM to 12 PM for essential repairs. We apologize for any inconvenience this may cause.',
      isNew: true
    }
  ];
}
