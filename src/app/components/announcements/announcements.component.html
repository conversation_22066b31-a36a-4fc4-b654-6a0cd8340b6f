<div class="p-4">
  <div
    *ngFor="let announcement of announcements"
    class="flex items-stretch justify-between gap-4 rounded-xl"
  >
    <div class="flex flex-col gap-1 flex-[2_2_0px]">
      <p class="text-[#617789] text-sm font-normal leading-normal" *ngIf="announcement.isNew">New</p>
      <p class="text-[#111518] text-base font-bold leading-tight">{{ announcement.title }}</p>
      <p class="text-[#617789] text-sm font-normal leading-normal">
        {{ announcement.content }}
      </p>
    </div>
    <div
      class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
      *ngIf="announcement.imageUrl"
      [style.background-image]="'url(' + announcement.imageUrl + ')'"
    ></div>
  </div>
</div>
