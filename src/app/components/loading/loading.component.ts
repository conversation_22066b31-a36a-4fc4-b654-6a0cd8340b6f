import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading',
  imports: [CommonModule],
  template: `
    <div class="flex items-center justify-center min-h-screen bg-white">
      <div class="text-center">
        <!-- Loading Spinner -->
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-700 mb-2">Loading...</h2>
        <p class="text-gray-500">Checking authentication status</p>
      </div>
    </div>
  `,
  styles: []
})
export class LoadingComponent {
}
