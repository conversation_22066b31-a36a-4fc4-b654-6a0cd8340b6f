import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MenuItem } from '../../models/dashboard.models';

@Component({
  selector: 'app-mobile-nav',
  imports: [CommonModule, RouterModule],
  templateUrl: './mobile-nav.component.html',
  styleUrl: './mobile-nav.component.css'
})
export class MobileNavComponent {
  menuItems: MenuItem[] = [
    { icon: 'House', label: 'Dashboard', route: '/dashboard' },
    { icon: 'Wrench', label: 'Maintenance', route: '/maintenance' },
    { icon: 'Calendar', label: 'Reservations', route: '/reservations' },
    { icon: 'Megaphone', label: 'Announcements', route: '/announcements' },
    { icon: 'File', label: 'Documents', route: '/documents' },
    { icon: 'Gear', label: 'Settings', route: '/settings' }
  ];

  constructor(private router: Router) {}

  isActiveRoute(route: string): boolean {
    return this.router.url === route;
  }
}
