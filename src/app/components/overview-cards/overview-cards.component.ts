import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OverviewCard } from '../../models/dashboard.models';

@Component({
  selector: 'app-overview-cards',
  imports: [CommonModule],
  templateUrl: './overview-cards.component.html',
  styleUrl: './overview-cards.component.css'
})
export class OverviewCardsComponent {
  overviewCards: OverviewCard[] = [
    { title: 'Pending Requests', value: 5, type: 'number' },
    { title: 'Upcoming Tasks', value: 12, type: 'number' },
    { title: 'Building Status', value: 'Operational', type: 'text' }
  ];
}
