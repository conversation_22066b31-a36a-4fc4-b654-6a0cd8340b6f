<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
  <div
    *ngFor="let card of overviewCards"
    class="flex flex-col gap-2 rounded-xl p-4 md:p-6 border border-[#dbe1e6]"
  >
    <p class="text-[#111518] text-sm md:text-base font-medium leading-normal">{{ card.title }}</p>
    <p class="text-[#111518] tracking-light text-xl md:text-2xl font-bold leading-tight">{{ card.value }}</p>
  </div>
</div>
