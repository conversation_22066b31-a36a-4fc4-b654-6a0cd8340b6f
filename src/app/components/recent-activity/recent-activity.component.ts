import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivityItem } from '../../models/dashboard.models';

@Component({
  selector: 'app-recent-activity',
  imports: [CommonModule],
  templateUrl: './recent-activity.component.html',
  styleUrl: './recent-activity.component.css'
})
export class RecentActivityComponent {
  activities: ActivityItem[] = [
    {
      icon: 'Wrench',
      title: 'New Maintenance Request',
      description: 'Request #12345'
    },
    {
      icon: 'MagnifyingGlass',
      title: 'Upcoming Building Inspection',
      description: 'Inspection on July 15th'
    },
    {
      icon: 'UsersThree',
      title: 'Community Meeting Scheduled',
      description: 'Meeting on July 20th'
    }
  ];
}
