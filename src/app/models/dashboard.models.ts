export interface MenuItem {
  icon: string;
  label: string;
  route?: string;
  isActive?: boolean;
}

export interface OverviewCard {
  title: string;
  value: string | number;
  type: 'number' | 'text';
}

export interface ActivityItem {
  icon: string;
  title: string;
  description: string;
  timestamp?: Date;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  isNew: boolean;
  imageUrl?: string;
}

export interface UserProfile {
  name: string;
  avatar?: string;
}

export interface MaintenanceRequest {
  id: string;
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Open' | 'In Progress' | 'Completed' | 'Cancelled';
  requestedBy: string;
  assignedTo?: string;
  createdDate: Date;
  dueDate?: Date;
  category: string;
}

export interface Reservation {
  id: string;
  facilityName: string;
  reservedBy: string;
  startTime: Date;
  endTime: Date;
  status: 'Confirmed' | 'Pending' | 'Cancelled';
  purpose: string;
  attendees?: number;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  uploadedBy: string;
  uploadedDate: Date;
  category: string;
  url: string;
}

export interface SettingsSection {
  title: string;
  description: string;
  settings: Setting[];
}

export interface Setting {
  key: string;
  label: string;
  type: 'toggle' | 'select' | 'input' | 'number';
  value: any;
  options?: string[];
  description?: string;
}
