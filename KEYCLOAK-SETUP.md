# Keycloak Configuration Guide

This guide explains how to set up Keycloak server and configure it for the Angular application.

## 🔧 Keycloak Server Setup

### Option 1: Docker (Recommended for Development)

```bash
# Run Keycloak with Docker
docker run -p 8080:8080 \
  -e KEYCLOAK_ADMIN=admin \
  -e KEYCLOAK_ADMIN_PASSWORD=admin \
  quay.io/keycloak/keycloak:latest \
  start-dev
```

### Option 2: Download and Run Locally

1. Download Keycloak from: https://www.keycloak.org/downloads
2. Extract and run:
```bash
cd keycloak-xx.x.x/bin
./kc.sh start-dev  # Linux/Mac
kc.bat start-dev   # Windows
```

## 🏗️ Keycloak Configuration Steps

### 1. Access Keycloak Admin Console
- Navigate to: `http://localhost:8080`
- Login with admin credentials (admin/admin if using Docker)

### 2. Create a Realm
1. Click on "Create Realm" or the dropdown next to "master"
2. Enter realm name: `your-realm-name` (update in environment.ts)
3. Click "Create"

### 3. Create a Client for Angular App
1. Go to "Clients" in the left sidebar
2. Click "Create client"
3. Configure the client:

**General Settings:**
- Client type: `OpenID Connect`
- Client ID: `angular-client` (must match environment.ts)
- Name: `Angular Keycloak App`
- Description: `Angular frontend application`

**Capability config:**
- Client authentication: `OFF` (public client)
- Authorization: `OFF`
- Standard flow: `ON` (Authorization Code Flow)
- Direct access grants: `ON` (for development)
- Implicit flow: `OFF`
- Service accounts roles: `OFF`

**Login settings:**
- Root URL: `http://localhost:4200`
- Home URL: `http://localhost:4200`
- Valid redirect URIs: 
  - `http://localhost:4200/*`
  - `http://localhost:4200/dashboard`
  - `http://localhost:4200/profile`
- Valid post logout redirect URIs: `http://localhost:4200/*`
- Web origins: `http://localhost:4200`

### 4. Configure Client Settings

Go to your client settings and verify:

**Access settings:**
- Standard flow enabled: `ON`
- Implicit flow enabled: `OFF`
- Direct access grants enabled: `ON`
- Service accounts enabled: `OFF`

**Authentication flow overrides:**
- Browser Flow: `browser`
- Direct Grant Flow: `direct grant`

### 5. Create Test Users
1. Go to "Users" in the left sidebar
2. Click "Add user"
3. Fill in user details:
   - Username: `testuser`
   - Email: `<EMAIL>`
   - First name: `Test`
   - Last name: `User`
   - Email verified: `ON`
   - Enabled: `ON`

4. Click "Create"
5. Go to "Credentials" tab
6. Click "Set password"
7. Enter password: `password123`
8. Set "Temporary": `OFF`
9. Click "Save"

### 6. Configure Realm Settings (Optional)

**Login settings:**
- User registration: `ON` (if you want self-registration)
- Forgot password: `ON`
- Remember me: `ON`
- Login with email: `ON`

**Tokens:**
- Access token lifespan: `5 minutes` (default)
- SSO session idle: `30 minutes`
- SSO session max: `10 hours`

## 🔗 Angular Configuration

Update your environment files with the correct Keycloak settings:

### src/environments/environment.ts
```typescript
export const environment = {
  production: false,
  keycloak: {
    url: 'http://localhost:8080',
    realm: 'your-realm-name',        // Replace with your realm name
    clientId: 'angular-client'       // Replace with your client ID
  }
};
```

### src/environments/environment.prod.ts
```typescript
export const environment = {
  production: true,
  keycloak: {
    url: 'https://your-keycloak-server.com',  // Your production Keycloak URL
    realm: 'your-realm-name',                 // Your realm name
    clientId: 'angular-client'                // Your client ID
  }
};
```

## 🚀 Testing the Integration

1. **Start Keycloak server** (if not already running)
2. **Start Angular development server:**
   ```bash
   npm start
   ```
3. **Navigate to:** `http://localhost:4200`
4. **Expected behavior:**
   - App loads with header showing "Login" button
   - Clicking Dashboard/Profile redirects to Keycloak login
   - After login, user is redirected back to the requested page
   - Header shows user info and "Logout" button

## 🔐 Security Considerations

### Production Settings

**Keycloak Client (Production):**
- Valid redirect URIs: Only your production domain
- Web origins: Only your production domain
- Use HTTPS for all URLs

**Realm Settings:**
- SSL required: `External requests`
- Content Security Policy: Configure appropriately

### HTTPS Configuration
For production, ensure:
1. Keycloak server runs on HTTPS
2. Angular app is served over HTTPS
3. Update all URLs in environment.prod.ts to use HTTPS

## 🛠️ Troubleshooting

### Common Issues

**1. CORS Errors:**
- Check "Web origins" in client settings
- Ensure Angular dev server URL is included

**2. Redirect URI Mismatch:**
- Verify "Valid redirect URIs" includes your Angular routes
- Check for trailing slashes

**3. Client Not Found:**
- Verify client ID matches environment.ts
- Ensure client is enabled

**4. Authentication Fails:**
- Check user credentials
- Verify user is enabled
- Check realm name matches

### Debug Steps

1. **Check browser console** for errors
2. **Verify Keycloak logs** in admin console
3. **Test with Keycloak account console:** `http://localhost:8080/realms/your-realm-name/account`
4. **Use browser network tab** to inspect authentication requests

## 📋 Keycloak vs Backend Explanation

### Why No Backend Needed for Authentication

**Direct Keycloak Integration (Current Setup):**
- ✅ Angular communicates directly with Keycloak
- ✅ Tokens managed by keycloak-angular library
- ✅ Simpler architecture for authentication-only needs
- ✅ Keycloak handles all OAuth2/OIDC flows
- ✅ Built-in token refresh and session management

**When You Need a Backend:**
- 🔧 Complex business logic beyond authentication
- 🔧 Database operations
- 🔧 API aggregation from multiple services
- 🔧 Custom authorization rules
- 🔧 File uploads/processing
- 🔧 Integration with legacy systems

### Current Architecture Benefits

1. **Simplified Setup:** No backend server to maintain
2. **Security:** Keycloak handles all security concerns
3. **Standards Compliant:** Full OAuth2/OIDC implementation
4. **Scalability:** Keycloak can handle thousands of users
5. **Features:** Built-in user management, MFA, social login

### When to Add Spring Boot Backend

Add a backend when you need:
- **Data Persistence:** Store application-specific data
- **Business Logic:** Complex operations beyond authentication
- **API Gateway:** Aggregate multiple microservices
- **Custom Endpoints:** Application-specific REST APIs

For pure authentication and user management, Keycloak alone is sufficient!

## 🎯 Next Steps

1. **Configure Keycloak** following this guide
2. **Update environment.ts** with your settings
3. **Test the application** with real authentication
4. **Add roles/permissions** if needed
5. **Configure production environment** when ready to deploy

The Angular app is now fully configured for direct Keycloak integration!
